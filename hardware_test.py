# 硬件测试脚本
# 用于测试激光笔和步进电机的连接

import time
from machine import FPIOA, Pin, Timer

def test_laser():
    """测试激光笔"""
    print("测试激光笔...")
    
    fpioa = FPIOA()
    fpioa.set_function(33, FPIOA.GPIO33)
    laser_pin = Pin(33, Pin.OUT)
    
    try:
        for i in range(5):
            print(f"激光笔开启 {i+1}/5")
            laser_pin.value(1)
            time.sleep_ms(500)
            
            print("激光笔关闭")
            laser_pin.value(0)
            time.sleep_ms(500)
        
        print("激光笔测试完成")
        return True
        
    except Exception as e:
        print(f"激光笔测试失败: {e}")
        return False
    finally:
        laser_pin.value(0)

def test_motors():
    """测试步进电机"""
    print("测试步进电机...")
    
    fpioa = FPIOA()
    
    # X轴电机引脚
    x_pins = [15, 16, 17, 19]
    # Y轴电机引脚
    y_pins = [27, 14, 61, 40]
    
    motor_pins = []
    
    try:
        # 初始化引脚
        for pin_num in x_pins + y_pins:
            fpioa.set_function(pin_num, getattr(FPIOA, f'GPIO{pin_num}'))
            motor_pins.append(Pin(pin_num, Pin.OUT))
        
        # 步进序列
        step_sequence = [
            [1, 1, 0, 0],
            [0, 1, 1, 0],
            [0, 0, 1, 1],
            [1, 0, 0, 1]
        ]
        
        print("测试X轴电机...")
        for cycle in range(3):
            for step in step_sequence:
                for i in range(4):
                    motor_pins[i].value(step[i])
                time.sleep_ms(10)
        
        # 停止X轴
        for i in range(4):
            motor_pins[i].value(0)
        
        time.sleep_ms(500)
        
        print("测试Y轴电机...")
        for cycle in range(3):
            for step in step_sequence:
                for i in range(4):
                    motor_pins[i+4].value(step[i])
                time.sleep_ms(10)
        
        # 停止Y轴
        for i in range(4, 8):
            motor_pins[i].value(0)
        
        print("步进电机测试完成")
        return True
        
    except Exception as e:
        print(f"步进电机测试失败: {e}")
        return False
    finally:
        # 确保所有电机停止
        for pin in motor_pins:
            pin.value(0)

def test_touch():
    """测试触摸屏"""
    print("测试触摸屏...")
    
    try:
        from machine import TOUCH
        tp = TOUCH(0)
        
        print("请触摸屏幕，5秒后结束测试...")
        start_time = time.ticks_ms()
        touch_count = 0
        
        while time.ticks_diff(time.ticks_ms(), start_time) < 5000:
            points = tp.read()
            if len(points) > 0:
                touch_count += 1
                point = points[0]
                print(f"触摸点 {touch_count}: ({point.x}, {point.y})")
                time.sleep_ms(200)  # 防止重复检测
        
        tp.deinit()
        print(f"触摸屏测试完成，检测到 {touch_count} 次触摸")
        return True
        
    except Exception as e:
        print(f"触摸屏测试失败: {e}")
        return False

def test_camera():
    """测试摄像头"""
    print("测试摄像头...")
    
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        sensor = Sensor(width=800, height=480)
        sensor.reset()
        sensor.set_hmirror(True)
        sensor.set_vflip(True)
        sensor.set_framesize(width=800, height=480)
        sensor.set_pixformat(Sensor.RGB565)
        
        Display.init(Display.LT9611, width=800, height=480, to_ide=True)
        MediaManager.init()
        sensor.run()
        
        print("摄像头初始化成功，拍摄测试图像...")
        
        for i in range(5):
            img = sensor.snapshot()
            img.draw_string_advanced(10, 10, 30, f"测试图像 {i+1}/5", color=(255, 0, 0))
            img.compressed_for_ide()
            Display.show_image(img)
            time.sleep_ms(1000)
        
        sensor.stop()
        Display.deinit()
        MediaManager.deinit()
        
        print("摄像头测试完成")
        return True
        
    except Exception as e:
        print(f"摄像头测试失败: {e}")
        return False

def run_all_tests():
    """运行所有硬件测试"""
    print("=== 硬件测试开始 ===")
    
    tests = [
        ("摄像头", test_camera),
        ("激光笔", test_laser),
        ("步进电机", test_motors),
        ("触摸屏", test_touch),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name}测试 ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"{test_name}测试异常: {e}")
            results[test_name] = False
        
        time.sleep_ms(1000)  # 测试间隔
    
    print("\n=== 测试结果汇总 ===")
    all_passed = True
    for test_name, result in results.items():
        status = "通过" if result else "失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n✅ 所有硬件测试通过！")
    else:
        print("\n❌ 部分硬件测试失败，请检查连接")
    
    return all_passed

if __name__ == "__main__":
    try:
        run_all_tests()
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试程序异常: {e}")
    finally:
        print("测试程序结束")

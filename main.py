# 简易自行瞄准装置 - 基本要求第2问
# 实现矩形识别、激光笔控制、步进电机控制和脱机调阈值功能

import time
import os
import sys
import math

from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, Timer, TOUCH

sensor = None

# 全局变量
laser_pin = None
motor_pins = []
step_num = 0
step_status_lst = [[1, 1, 0, 0], [0, 1, 1, 0], [0, 0, 1, 1], [1, 0, 0, 1]]
motor_timer = None
motor_x_dir = 0  # 0停止, 1正转, -1反转
motor_y_dir = 0
target_x = 0
target_y = 0
current_x = 0
current_y = 0
aim_success_time = 0
laser_fired = False

# 阈值相关
threshold_dict = {'rect': [(80, 255)]}  # 矩形识别阈值
touch_counter = 0
flag = 0  # 0=正常模式, 1=瞄准模式, 2=调阈值模式
tp = None

# 图像处理参数
cut_roi = (0, 0, 800, 480)  # 感兴趣区域
rect_center_x = 400  # 矩形中心X坐标
rect_center_y = 240  # 矩形中心Y坐标

def init_hardware():
    """初始化硬件"""
    global laser_pin, motor_pins, motor_timer, tp

    # 初始化FPIOA
    fpioa = FPIOA()

    # 激光笔控制引脚 GPIO33
    fpioa.set_function(33, FPIOA.GPIO33)
    laser_pin = Pin(33, Pin.OUT)
    laser_pin.value(0)  # 初始关闭激光笔

    # 步进电机引脚 GPIO15, 16, 17, 19 (X轴)
    fpioa.set_function(15, FPIOA.GPIO15)
    fpioa.set_function(16, FPIOA.GPIO16)
    fpioa.set_function(17, FPIOA.GPIO17)
    fpioa.set_function(19, FPIOA.GPIO19)

    pin_A1 = Pin(15, Pin.OUT)
    pin_B1 = Pin(16, Pin.OUT)
    pin_C1 = Pin(17, Pin.OUT)
    pin_D1 = Pin(19, Pin.OUT)

    # Y轴步进电机引脚 GPIO27, 14, 61, 40
    fpioa.set_function(27, FPIOA.GPIO27)
    fpioa.set_function(14, FPIOA.GPIO14)
    fpioa.set_function(61, FPIOA.GPIO61)
    fpioa.set_function(40, FPIOA.GPIO40)

    pin_A2 = Pin(27, Pin.OUT)
    pin_B2 = Pin(14, Pin.OUT)
    pin_C2 = Pin(61, Pin.OUT)
    pin_D2 = Pin(40, Pin.OUT)

    motor_pins = [pin_A1, pin_B1, pin_C1, pin_D1, pin_A2, pin_B2, pin_C2, pin_D2]

    # 初始化步进电机定时器
    motor_timer = Timer(-1)
    motor_timer.init(period=5, mode=Timer.PERIODIC, callback=motor_step_callback)

    # 初始化触摸屏
    try:
        tp = TOUCH(0)
        print("触摸屏初始化成功")
    except Exception as e:
        print(f"触摸屏初始化失败: {e}")
        tp = None

    print("硬件初始化完成")

def motor_step_callback(arg):
    """步进电机回调函数"""
    global step_num, motor_x_dir, motor_y_dir, motor_pins, step_status_lst

    # X轴电机控制
    if motor_x_dir != 0:
        if motor_x_dir == 1:
            step_num = (step_num + 1) % 4
        else:
            step_num = (step_num - 1 + 4) % 4

        step_status = step_status_lst[step_num]
        for i in range(4):
            motor_pins[i].value(step_status[i])
    else:
        # 停止X轴电机
        for i in range(4):
            motor_pins[i].value(0)

    # Y轴电机控制 (简化版，实际应该独立控制)
    if motor_y_dir != 0:
        step_status = step_status_lst[step_num]
        for i in range(4, 8):
            motor_pins[i].value(step_status[i-4] if motor_y_dir == 1 else step_status[3-(i-4)])
    else:
        # 停止Y轴电机
        for i in range(4, 8):
            motor_pins[i].value(0)

def detect_rectangle(img):
    """检测矩形并返回中心坐标"""
    global threshold_dict

    # 转换为灰度图像
    img_rect = img.to_grayscale(copy=True)

    # 二值化
    img_rect = img_rect.binary(threshold_dict['rect'])

    # 查找矩形
    rects = img_rect.find_rects(threshold=5000)

    if rects:
        # 选择最大的矩形
        largest_rect = max(rects, key=lambda r: r.w() * r.h())

        # 获取矩形的四个角点
        corners = largest_rect.corners()

        # 绘制矩形边框
        for i in range(4):
            next_i = (i + 1) % 4
            img.draw_line(corners[i][0], corners[i][1],
                         corners[next_i][0], corners[next_i][1],
                         color=(0, 255, 0), thickness=3)

        # 计算矩形中心
        center_x = sum([corner[0] for corner in corners]) // 4
        center_y = sum([corner[1] for corner in corners]) // 4

        # 绘制中心点
        img.draw_circle(center_x, center_y, 5, color=(255, 0, 0), thickness=2, fill=True)

        return center_x, center_y, True

    return 0, 0, False

def control_laser_aim(rect_x, rect_y):
    """控制激光笔瞄准矩形中心"""
    global motor_x_dir, motor_y_dir, laser_pin, aim_success_time, laser_fired

    # 图像中心坐标
    center_x = 400
    center_y = 240

    # 计算偏差
    error_x = rect_x - center_x
    error_y = rect_y - center_y

    # 死区设置，避免抖动
    dead_zone = 8

    # X轴控制
    if abs(error_x) > dead_zone:
        if error_x > 0:
            motor_x_dir = 1  # 向右
        else:
            motor_x_dir = -1  # 向左
    else:
        motor_x_dir = 0  # 停止

    # Y轴控制
    if abs(error_y) > dead_zone:
        if error_y > 0:
            motor_y_dir = 1  # 向下
        else:
            motor_y_dir = -1  # 向上
    else:
        motor_y_dir = 0  # 停止

    # 瞄准成功判断
    if abs(error_x) <= dead_zone and abs(error_y) <= dead_zone:
        if aim_success_time == 0:
            aim_success_time = time.ticks_ms()

        # 瞄准稳定100ms后发射激光（打靶）
        if time.ticks_diff(time.ticks_ms(), aim_success_time) > 100:
            if not laser_fired:
                laser_pin.value(1)
                time.sleep_ms(50)  # 激光脉冲50ms
                laser_pin.value(0)
                laser_fired = True
                return "发射完成"
            else:
                return "已发射"
        else:
            return "瞄准稳定中"
    else:
        # 重新瞄准，重置状态
        aim_success_time = 0
        laser_fired = False
        laser_pin.value(0)
        return "瞄准中"

def witch_key(x, y):
    """判断触摸位置对应的按键"""
    # 简化的按键区域判断
    if y < 100:  # 顶部区域
        if x < 200:
            return "0"  # 减少阈值下限
        elif x < 400:
            return "6"  # 增加阈值下限
        elif x < 600:
            return "1"  # 减少阈值上限
        else:
            return "7"  # 增加阈值上限
    elif y > 380:  # 底部区域
        if x < 200:
            return "save"
        elif x < 400:
            return "reset"
        elif x < 600:
            return "change"
        else:
            return "return"
    return None

def show_img_2_screen(img):
    """显示图像到屏幕"""
    # 由于启用了镜像和翻转，需要相应调整显示
    img.compressed_for_ide()
    Display.show_image(img)

def threshold_adjustment_mode():
    """阈值调整模式"""
    global threshold_dict, tp, flag, sensor

    # 界面颜色
    button_color = (100, 100, 100)
    text_color = (255, 255, 255)

    threshold_current = list(threshold_dict['rect'][0])  # 复制当前阈值

    while True:
        os.exitpoint()
        img = sensor.snapshot()

        # 应用当前阈值进行二值化预览
        img_preview = img.to_grayscale(copy=True)
        img_preview = img_preview.binary([threshold_current])

        # 在右侧显示二值化预览
        preview_small = img_preview.copy(roi=(0, 0, 300, 200))
        preview_small = preview_small.to_rgb565()
        img.draw_image(preview_small, 480, 20)
        img.draw_rectangle(480, 20, 300, 200, color=(255, 255, 255), thickness=2, fill=False)

        # 绘制调整界面背景
        img.draw_rectangle(20, 20, 450, 440, color=(50, 50, 50), thickness=2, fill=True)

        # 标题
        img.draw_string_advanced(30, 30, 30, "Threshold Adjust", color=text_color)
        img.draw_string_advanced(30, 70, 25, f"Current: [{threshold_current[0]}, {threshold_current[1]}]", color=text_color)

        # 下限调整按钮
        img.draw_rectangle(30, 120, 80, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(40, 130, 25, "Min-", color=text_color)

        img.draw_rectangle(130, 120, 80, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(140, 130, 25, "Min+", color=text_color)

        img.draw_string_advanced(230, 135, 25, f"Min: {threshold_current[0]}", color=text_color)

        # 上限调整按钮
        img.draw_rectangle(30, 180, 80, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(40, 190, 25, "Max-", color=text_color)

        img.draw_rectangle(130, 180, 80, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(140, 190, 25, "Max+", color=text_color)

        img.draw_string_advanced(230, 195, 25, f"Max: {threshold_current[1]}", color=text_color)

        # 功能按钮
        img.draw_rectangle(30, 280, 100, 40, color=(0, 150, 0), thickness=2, fill=True)
        img.draw_string_advanced(40, 290, 25, "SAVE", color=text_color)

        img.draw_rectangle(150, 280, 100, 40, color=(150, 150, 0), thickness=2, fill=True)
        img.draw_string_advanced(160, 290, 25, "RESET", color=text_color)

        img.draw_rectangle(270, 280, 100, 40, color=(150, 0, 0), thickness=2, fill=True)
        img.draw_string_advanced(280, 290, 25, "BACK", color=text_color)

        # 使用说明
        img.draw_string_advanced(30, 350, 20, "Help:", color=text_color)
        img.draw_string_advanced(30, 375, 18, "- Adjust threshold", color=text_color)
        img.draw_string_advanced(30, 395, 18, "- White = target", color=text_color)
        img.draw_string_advanced(30, 415, 18, "- Save to apply", color=text_color)

        # 检测触摸
        if tp is not None:
            try:
                points = tp.read()
                if points and len(points) > 0:
                    x, y = points[0].x, points[0].y
                    print(f"阈值调整模式触摸: ({x}, {y})")

                    # 判断按钮点击
                    if 30 <= x <= 110 and 120 <= y <= 160:  # 下限-
                        threshold_current[0] = max(0, threshold_current[0] - 5)
                        print(f"下限-: {threshold_current[0]}")
                        time.sleep_ms(200)
                    elif 130 <= x <= 210 and 120 <= y <= 160:  # 下限+
                        threshold_current[0] = min(threshold_current[1] - 1, threshold_current[0] + 5)
                        print(f"下限+: {threshold_current[0]}")
                        time.sleep_ms(200)
                    elif 30 <= x <= 110 and 180 <= y <= 220:  # 上限-
                        threshold_current[1] = max(threshold_current[0] + 1, threshold_current[1] - 5)
                        print(f"上限-: {threshold_current[1]}")
                        time.sleep_ms(200)
                    elif 130 <= x <= 210 and 180 <= y <= 220:  # 上限+
                        threshold_current[1] = min(255, threshold_current[1] + 5)
                        print(f"上限+: {threshold_current[1]}")
                        time.sleep_ms(200)
                    elif 30 <= x <= 130 and 280 <= y <= 320:  # 保存
                        threshold_dict['rect'] = [tuple(threshold_current)]
                        print(f"保存阈值: {threshold_dict['rect']}")
                        # 显示保存成功提示 - 修正镜像翻转
                        img.draw_rectangle(150, 350, 200, 40, color=(0, 255, 0), thickness=2, fill=True)
                        img.draw_string(790-350, 470-390, "SAVED!", color=(0, 0, 0), scale=2)
                        img.compressed_for_ide()
                        Display.show_image(img)
                        time.sleep_ms(1000)
                    elif 150 <= x <= 250 and 280 <= y <= 320:  # 重置
                        threshold_current = [80, 255]
                        print("重置阈值")
                        time.sleep_ms(300)
                    elif 270 <= x <= 370 and 280 <= y <= 320:  # 返回
                        flag = 0
                        print("=== 退出阈值调整模式 ===")
                        time.sleep_ms(300)
                        break
            except Exception as e:
                print(f"阈值调整触摸错误: {e}")

        img.compressed_for_ide()
        Display.show_image(img)

try:

    # 初始化传感器
    sensor = Sensor(width=800, height=480)
    sensor.reset()
    sensor.set_hmirror(True)  # 启用镜像
    sensor.set_vflip(True)    # 启用翻转
    sensor.set_framesize(width=800, height=480)
    sensor.set_pixformat(Sensor.RGB565)

    # 初始化显示
    Display.init(Display.ST7701, width=800, height=480, to_ide=True)
    MediaManager.init()
    sensor.run()

    # 初始化硬件
    init_hardware()

    # 时钟
    clock = time.clock()

    print("系统初始化完成")
    print("长按屏幕进入阈值调整模式")
    print("检测到矩形后自动开始瞄准")

    while True:
        clock.tick()
        os.exitpoint()

        img = sensor.snapshot()
        img = img.copy(roi=cut_roi)

        # 检测触摸，长按进入阈值调整模式
        if tp is not None:
            try:
                points = tp.read()
                if points and len(points) > 0:
                    touch_counter += 1
                    point = points[0]
                    print(f"触摸检测: 计数={touch_counter}, 坐标=({point.x}, {point.y})")
                    if touch_counter >= 15:  # 长按约0.5秒
                        flag = 2  # 进入阈值调整模式
                        touch_counter = 0
                        print("=== 进入阈值调整模式 ===")
                else:
                    if touch_counter > 0:
                        touch_counter = max(0, touch_counter - 1)
            except Exception as e:
                print(f"触摸检测错误: {e}")
                touch_counter = 0
        else:
            print("触摸屏未初始化")

        # 根据模式执行不同逻辑
        if flag == 2:  # 阈值调整模式
            threshold_adjustment_mode()
            continue

        # 正常工作模式
        # 检测矩形
        rect_x, rect_y, rect_found = detect_rectangle(img)

        if rect_found:
            # 显示矩形信息
            img.draw_string_advanced(10, 10, 30, f"Target: ({rect_x}, {rect_y})", color=(255, 255, 0))

            # 自动瞄准
            aim_status = control_laser_aim(rect_x, rect_y)

            if "完成" in aim_status:
                img.draw_string_advanced(10, 50, 30, "SHOT!", color=(0, 255, 0))
            elif "已发射" in aim_status:
                img.draw_string_advanced(10, 50, 30, "FIRED", color=(0, 255, 0))
            else:
                img.draw_string_advanced(10, 50, 30, "AIMING", color=(255, 255, 0))
        else:
            # 未检测到矩形，停止电机和激光笔，重置状态
            motor_x_dir = 0
            motor_y_dir = 0
            laser_pin.value(0)
            aim_success_time = 0
            laser_fired = False
            img.draw_string_advanced(10, 10, 30, "NO TARGET", color=(255, 0, 0))

        # 显示系统状态
        img.draw_string_advanced(10, 90, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
        img.draw_string_advanced(10, 120, 25, f"Laser: {'ON' if laser_pin.value() else 'OFF'}", color=(255, 255, 255))
        img.draw_string_advanced(10, 150, 25, f"Motor X:{motor_x_dir} Y:{motor_y_dir}", color=(255, 255, 255))
        img.draw_string_advanced(10, 180, 25, f"Touch: {touch_counter}/15", color=(200, 200, 200))
        img.draw_string_advanced(10, 210, 20, "Long press for threshold", color=(200, 200, 200))

        # 显示触摸状态
        if tp is not None:
            try:
                test_points = tp.read()
                if test_points and len(test_points) > 0:
                    point = test_points[0]
                    img.draw_string_advanced(10, 240, 20, f"Touch: ({point.x}, {point.y})", color=(0, 255, 255))
                    img.draw_circle(point.x, point.y, 10, color=(0, 255, 255), thickness=2, fill=False)
            except:
                pass

        # 绘制瞄准十字线
        center_x, center_y = 400, 240
        img.draw_line(center_x - 20, center_y, center_x + 20, center_y, color=(255, 0, 255), thickness=2)
        img.draw_line(center_x, center_y - 20, center_x, center_y + 20, color=(255, 0, 255), thickness=2)

        show_img_2_screen(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    # 清理资源
    if motor_timer:
        motor_timer.deinit()
    if laser_pin:
        laser_pin.value(0)  # 关闭激光笔
    if motor_pins:
        for pin in motor_pins:
            pin.value(0)  # 停止所有电机
    if tp:
        tp.deinit()
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("系统已安全关闭")

# 简易自行瞄准装置 - 优化版本
# 使用配置文件，代码结构更清晰

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, Timer, TOUCH
from config import get_config

class AimingSystem:
    def __init__(self):
        self.config = get_config()
        self.sensor = None
        self.laser_pin = None
        self.motor_pins = []
        self.motor_timer = None
        self.tp = None
        
        # 控制变量
        self.step_num = 0
        self.motor_x_dir = 0
        self.motor_y_dir = 0
        self.flag = 0  # 0=正常, 2=调阈值
        self.touch_counter = 0
        self.aim_success_time = 0
        
        # 阈值设置
        self.threshold_dict = {
            'rect': [self.config['detection']['DEFAULT_THRESHOLD']]
        }
    
    def init_hardware(self):
        """初始化硬件"""
        print("初始化硬件...")
        
        fpioa = FPIOA()
        
        # 激光笔
        laser_pin_num = self.config['gpio']['LASER_PIN']
        fpioa.set_function(laser_pin_num, getattr(FPIOA, f'GPIO{laser_pin_num}'))
        self.laser_pin = Pin(laser_pin_num, Pin.OUT)
        self.laser_pin.value(0)
        
        # 步进电机
        motor_pins = []
        all_pins = self.config['gpio']['MOTOR_X_PINS'] + self.config['gpio']['MOTOR_Y_PINS']
        
        for pin_num in all_pins:
            fpioa.set_function(pin_num, getattr(FPIOA, f'GPIO{pin_num}'))
            motor_pins.append(Pin(pin_num, Pin.OUT))
        
        self.motor_pins = motor_pins
        
        # 定时器
        self.motor_timer = Timer(-1)
        self.motor_timer.init(
            period=self.config['control']['MOTOR_TIMER_PERIOD'],
            mode=Timer.PERIODIC,
            callback=self.motor_callback
        )
        
        # 触摸屏
        self.tp = TOUCH(0)
        
        print("硬件初始化完成")
    
    def init_sensor(self):
        """初始化传感器"""
        print("初始化传感器...")
        
        img_config = self.config['image']
        
        self.sensor = Sensor(width=img_config['SENSOR_WIDTH'], 
                           height=img_config['SENSOR_HEIGHT'])
        self.sensor.reset()
        self.sensor.set_hmirror(img_config['HMIRROR'])
        self.sensor.set_vflip(img_config['VFLIP'])
        self.sensor.set_framesize(width=img_config['SENSOR_WIDTH'], 
                                height=img_config['SENSOR_HEIGHT'])
        self.sensor.set_pixformat(Sensor.RGB565)
        
        Display.init(Display.LT9611, 
                    width=img_config['SENSOR_WIDTH'], 
                    height=img_config['SENSOR_HEIGHT'], 
                    to_ide=True)
        MediaManager.init()
        self.sensor.run()
        
        print("传感器初始化完成")
    
    def motor_callback(self, arg):
        """步进电机控制回调"""
        motor_steps = self.config['motor_steps']
        
        # X轴控制
        if self.motor_x_dir != 0:
            if self.motor_x_dir == 1:
                self.step_num = (self.step_num + 1) % 4
            else:
                self.step_num = (self.step_num - 1 + 4) % 4
            
            step_status = motor_steps[self.step_num]
            for i in range(4):
                self.motor_pins[i].value(step_status[i])
        else:
            for i in range(4):
                self.motor_pins[i].value(0)
        
        # Y轴控制
        if self.motor_y_dir != 0:
            step_status = motor_steps[self.step_num]
            for i in range(4, 8):
                pin_idx = i - 4
                value = step_status[pin_idx] if self.motor_y_dir == 1 else step_status[3-pin_idx]
                self.motor_pins[i].value(value)
        else:
            for i in range(4, 8):
                self.motor_pins[i].value(0)
    
    def detect_rectangle(self, img):
        """检测矩形"""
        detection_config = self.config['detection']
        display_config = self.config['display']
        
        img_gray = img.to_grayscale(copy=True)
        img_binary = img_gray.binary(self.threshold_dict['rect'])
        rects = img_binary.find_rects(threshold=detection_config['MIN_RECT_AREA'])
        
        if rects:
            # 选择最大矩形
            largest = max(rects, key=lambda r: r.w() * r.h())
            corners = largest.corners()
            
            # 绘制矩形
            for i in range(4):
                next_i = (i + 1) % 4
                img.draw_line(corners[i][0], corners[i][1], 
                             corners[next_i][0], corners[next_i][1], 
                             color=display_config['SUCCESS_COLOR'], 
                             thickness=display_config['RECT_THICKNESS'])
            
            # 计算中心
            center_x = sum([c[0] for c in corners]) // 4
            center_y = sum([c[1] for c in corners]) // 4
            
            img.draw_circle(center_x, center_y, 
                          display_config['CIRCLE_RADIUS'], 
                          color=display_config['ERROR_COLOR'], 
                          thickness=display_config['LINE_THICKNESS'], 
                          fill=True)
            
            return center_x, center_y, True
        
        return 0, 0, False
    
    def control_aim(self, rect_x, rect_y):
        """控制瞄准"""
        img_config = self.config['image']
        control_config = self.config['control']
        
        center_x = img_config['CENTER_X']
        center_y = img_config['CENTER_Y']
        error_x = rect_x - center_x
        error_y = rect_y - center_y
        dead_zone = control_config['DEAD_ZONE']
        
        # 控制电机
        self.motor_x_dir = 1 if error_x > dead_zone else (-1 if error_x < -dead_zone else 0)
        self.motor_y_dir = 1 if error_y > dead_zone else (-1 if error_y < -dead_zone else 0)
        
        # 瞄准成功判断
        if abs(error_x) <= dead_zone and abs(error_y) <= dead_zone:
            if self.aim_success_time == 0:
                self.aim_success_time = time.ticks_ms()
            
            # 激光控制
            laser_duration = control_config['LASER_DURATION']
            if time.ticks_diff(time.ticks_ms(), self.aim_success_time) < laser_duration:
                self.laser_pin.value(1)
                return "激光发射中"
            else:
                self.laser_pin.value(0)
                return "发射完成"
        else:
            self.aim_success_time = 0
            self.laser_pin.value(0)
            return "瞄准中"
    
    def threshold_adjustment_mode(self):
        """阈值调整模式"""
        display_config = self.config['display']
        detection_config = self.config['detection']
        
        threshold_current = list(self.threshold_dict['rect'][0])
        
        while True:
            os.exitpoint()
            img = self.sensor.snapshot()
            
            # 二值化预览
            img_preview = img.to_grayscale(copy=True)
            img_preview = img_preview.binary([threshold_current])
            preview_small = img_preview.copy(roi=(0, 0, 300, 200))
            preview_small = preview_small.to_rgb565()
            img.draw_image(preview_small, 480, 20)
            img.draw_rectangle(480, 20, 300, 200, 
                             color=display_config['TEXT_COLOR'], 
                             thickness=2, fill=False)
            
            # 绘制界面
            self.draw_threshold_ui(img, threshold_current, display_config)
            
            # 处理触摸
            if self.handle_threshold_touch(threshold_current, detection_config):
                break
            
            img.compressed_for_ide()
            Display.show_image(img)
    
    def draw_threshold_ui(self, img, threshold_current, display_config):
        """绘制阈值调整界面"""
        # 背景
        img.draw_rectangle(20, 20, 450, 440, 
                         color=(50, 50, 50), thickness=2, fill=True)
        
        # 标题和信息
        img.draw_string_advanced(30, 30, display_config['TITLE_FONT_SIZE'], 
                               "矩形阈值调整", color=display_config['TEXT_COLOR'])
        img.draw_string_advanced(30, 70, display_config['NORMAL_FONT_SIZE'], 
                               f"当前: [{threshold_current[0]}, {threshold_current[1]}]", 
                               color=display_config['TEXT_COLOR'])
        
        # 按钮
        buttons = [
            (30, 120, "下限-"), (130, 120, "下限+"),
            (30, 180, "上限-"), (130, 180, "上限+"),
            (30, 280, "保存"), (150, 280, "重置"), (270, 280, "返回")
        ]
        
        for x, y, text in buttons:
            color = display_config['BUTTON_COLOR']
            if text == "保存":
                color = (0, 150, 0)
            elif text == "返回":
                color = (150, 0, 0)
            elif text == "重置":
                color = (150, 150, 0)
            
            img.draw_rectangle(x, y, 100, 40, color=color, thickness=2, fill=True)
            img.draw_string_advanced(x+10, y+10, display_config['NORMAL_FONT_SIZE'], 
                                   text, color=display_config['TEXT_COLOR'])
    
    def handle_threshold_touch(self, threshold_current, detection_config):
        """处理阈值调整触摸"""
        points = self.tp.read()
        if len(points) > 0:
            x, y = points[0].x, points[0].y
            step = detection_config['THRESHOLD_STEP']
            
            if 30 <= x <= 130 and 120 <= y <= 160:  # 下限-
                threshold_current[0] = max(0, threshold_current[0] - step)
            elif 130 <= x <= 230 and 120 <= y <= 160:  # 下限+
                threshold_current[0] = min(threshold_current[1] - 1, threshold_current[0] + step)
            elif 30 <= x <= 130 and 180 <= y <= 220:  # 上限-
                threshold_current[1] = max(threshold_current[0] + 1, threshold_current[1] - step)
            elif 130 <= x <= 230 and 180 <= y <= 220:  # 上限+
                threshold_current[1] = min(255, threshold_current[1] + step)
            elif 30 <= x <= 130 and 280 <= y <= 320:  # 保存
                self.threshold_dict['rect'] = [tuple(threshold_current)]
                return False  # 不退出，显示保存成功
            elif 150 <= x <= 250 and 280 <= y <= 320:  # 重置
                threshold_current[0] = self.config['detection']['DEFAULT_THRESHOLD'][0]
                threshold_current[1] = self.config['detection']['DEFAULT_THRESHOLD'][1]
            elif 270 <= x <= 370 and 280 <= y <= 320:  # 返回
                self.flag = 0
                return True
            
            time.sleep_ms(200)
        
        return False
    
    def run(self):
        """主运行循环"""
        try:
            print("简易自行瞄准装置启动...")
            
            self.init_sensor()
            self.init_hardware()
            
            clock = time.clock()
            
            print("系统就绪")
            
            while True:
                clock.tick()
                os.exitpoint()
                
                img = self.sensor.snapshot()
                
                # 检测长按进入调阈值模式
                points = self.tp.read()
                if len(points) > 0:
                    self.touch_counter += 1
                    if self.touch_counter > self.config['control']['TOUCH_LONG_PRESS']:
                        self.flag = 2
                        self.touch_counter = 0
                else:
                    self.touch_counter = max(0, self.touch_counter - 2)
                
                # 模式切换
                if self.flag == 2:
                    self.threshold_adjustment_mode()
                    continue
                
                # 正常工作模式
                rect_x, rect_y, found = self.detect_rectangle(img)
                
                if found:
                    status = self.control_aim(rect_x, rect_y)
                    self.draw_status_info(img, rect_x, rect_y, status, clock.fps())
                else:
                    self.stop_all()
                    self.draw_no_target_info(img, clock.fps())
                
                # 绘制瞄准线
                self.draw_crosshair(img)
                
                img.compressed_for_ide()
                Display.show_image(img)
                
        except KeyboardInterrupt:
            print("用户停止")
        except Exception as e:
            print(f"异常: {e}")
        finally:
            self.cleanup()
    
    def draw_status_info(self, img, rect_x, rect_y, status, fps):
        """绘制状态信息"""
        display_config = self.config['display']
        
        img.draw_string_advanced(10, 10, display_config['NORMAL_FONT_SIZE'], 
                               f"目标: ({rect_x}, {rect_y})", 
                               color=display_config['WARNING_COLOR'])
        img.draw_string_advanced(10, 40, display_config['NORMAL_FONT_SIZE'], 
                               f"状态: {status}", 
                               color=display_config['SUCCESS_COLOR'])
        
        if self.config['debug']['SHOW_FPS']:
            img.draw_string_advanced(10, 70, display_config['SMALL_FONT_SIZE'], 
                                   f"FPS: {fps:.1f}", 
                                   color=display_config['INFO_COLOR'])
    
    def draw_no_target_info(self, img, fps):
        """绘制无目标信息"""
        display_config = self.config['display']
        
        img.draw_string_advanced(10, 10, display_config['NORMAL_FONT_SIZE'], 
                               "未检测到矩形", 
                               color=display_config['ERROR_COLOR'])
        
        if self.config['debug']['SHOW_FPS']:
            img.draw_string_advanced(10, 40, display_config['SMALL_FONT_SIZE'], 
                                   f"FPS: {fps:.1f}", 
                                   color=display_config['INFO_COLOR'])
    
    def draw_crosshair(self, img):
        """绘制瞄准十字线"""
        img_config = self.config['image']
        display_config = self.config['display']
        
        center_x = img_config['CENTER_X']
        center_y = img_config['CENTER_Y']
        
        img.draw_line(center_x - 20, center_y, center_x + 20, center_y, 
                     color=(255, 0, 255), thickness=display_config['LINE_THICKNESS'])
        img.draw_line(center_x, center_y - 20, center_x, center_y + 20, 
                     color=(255, 0, 255), thickness=display_config['LINE_THICKNESS'])
    
    def stop_all(self):
        """停止所有动作"""
        self.motor_x_dir = 0
        self.motor_y_dir = 0
        self.laser_pin.value(0)
        self.aim_success_time = 0
    
    def cleanup(self):
        """清理资源"""
        print("清理资源...")
        
        if self.motor_timer:
            self.motor_timer.deinit()
        if self.laser_pin:
            self.laser_pin.value(0)
        if self.motor_pins:
            for pin in self.motor_pins:
                pin.value(0)
        if self.tp:
            self.tp.deinit()
        if isinstance(self.sensor, Sensor):
            self.sensor.stop()
        
        Display.deinit()
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        time.sleep_ms(100)
        MediaManager.deinit()
        
        print("系统已安全关闭")

# 主程序入口
if __name__ == "__main__":
    system = AimingSystem()
    system.run()

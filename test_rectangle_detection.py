# 矩形检测测试脚本
# 用于测试矩形识别功能和阈值调整

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, TOUCH

sensor = None

# 测试用阈值
threshold_dict = {'rect': [(80, 255)]}
tp = None

def test_rectangle_detection():
    """测试矩形检测功能"""
    global sensor, tp, threshold_dict
    
    try:
        print("矩形检测测试启动...")
        
        # 初始化传感器
        sensor = Sensor(width=800, height=480)
        sensor.reset()
        sensor.set_hmirror(True)
        sensor.set_vflip(True)
        sensor.set_framesize(width=800, height=480)
        sensor.set_pixformat(Sensor.RGB565)
        
        # 初始化显示
        Display.init(Display.LT9611, width=800, height=480, to_ide=True)
        MediaManager.init()
        sensor.run()
        
        # 初始化触摸屏
        tp = TOUCH(0)
        
        clock = time.clock()
        
        print("测试开始，按Ctrl+C停止")
        
        while True:
            clock.tick()
            os.exitpoint()
            
            img = sensor.snapshot()
            
            # 矩形检测
            img_rect = img.to_grayscale(copy=True)
            img_rect = img_rect.binary(threshold_dict['rect'])
            rects = img_rect.find_rects(threshold=5000)
            
            rect_count = 0
            if rects:
                rect_count = len(rects)
                for rect in rects:
                    # 获取矩形的四个角点
                    corners = rect.corners()
                    
                    # 绘制矩形边框
                    for i in range(4):
                        next_i = (i + 1) % 4
                        img.draw_line(corners[i][0], corners[i][1], 
                                     corners[next_i][0], corners[next_i][1], 
                                     color=(0, 255, 0), thickness=3)
                    
                    # 计算并绘制中心点
                    center_x = sum([corner[0] for corner in corners]) // 4
                    center_y = sum([corner[1] for corner in corners]) // 4
                    img.draw_circle(center_x, center_y, 8, color=(255, 0, 0), thickness=2, fill=True)
                    
                    # 显示坐标信息
                    img.draw_string_advanced(center_x + 15, center_y - 15, 25, 
                                           f"({center_x},{center_y})", color=(255, 255, 0))
            
            # 显示检测信息
            img.draw_string_advanced(10, 10, 30, f"检测到矩形: {rect_count}", 
                                   color=(255, 255, 255) if rect_count > 0 else (255, 0, 0))
            img.draw_string_advanced(10, 50, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
            img.draw_string_advanced(10, 80, 25, f"阈值: {threshold_dict['rect'][0]}", color=(255, 255, 255))
            
            # 绘制中心十字线
            center_x, center_y = 400, 240
            img.draw_line(center_x - 30, center_y, center_x + 30, center_y, 
                         color=(255, 0, 255), thickness=2)
            img.draw_line(center_x, center_y - 30, center_x, center_y + 30, 
                         color=(255, 0, 255), thickness=2)
            
            # 显示二值化预览（小窗口）
            preview = img.to_grayscale(copy=True)
            preview = preview.binary(threshold_dict['rect'])
            preview = preview.to_rgb565()
            preview = preview.copy(roi=(0, 0, 200, 150))  # 缩小预览窗口
            img.draw_image(preview, 580, 10)  # 在右上角显示
            img.draw_rectangle(580, 10, 200, 150, color=(255, 255, 255), thickness=2, fill=False)
            img.draw_string_advanced(590, 170, 20, "二值化预览", color=(255, 255, 255))
            
            img.compressed_for_ide()
            Display.show_image(img)
            
    except KeyboardInterrupt:
        print("测试停止")
    except Exception as e:
        print(f"测试异常: {e}")
    finally:
        if tp:
            tp.deinit()
        if isinstance(sensor, Sensor):
            sensor.stop()
        Display.deinit()
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        time.sleep_ms(100)
        MediaManager.deinit()
        print("测试结束")

if __name__ == "__main__":
    test_rectangle_detection()

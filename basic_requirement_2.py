# 基本要求第2问 - 简易自行瞄准装置
# 实现矩形识别、激光笔控制、步进电机控制

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, Timer, TOUCH

# 全局变量
sensor = None
laser_pin = None
motor_pins = []
motor_timer = None
tp = None

# 步进电机控制
step_num = 0
step_status_lst = [[1, 1, 0, 0], [0, 1, 1, 0], [0, 0, 1, 1], [1, 0, 0, 1]]
motor_x_dir = 0  # 0停止, 1正转, -1反转
motor_y_dir = 0

# 矩形识别阈值
threshold_rect = [(80, 255)]

# 瞄准参数
aim_success_time = 0
laser_on_duration = 4000  # 激光开启4秒

def init_hardware():
    """初始化硬件"""
    global laser_pin, motor_pins, motor_timer, tp
    
    fpioa = FPIOA()
    
    # 激光笔 GPIO33
    fpioa.set_function(33, FPIOA.GPIO33)
    laser_pin = Pin(33, Pin.OUT)
    laser_pin.value(0)
    
    # X轴步进电机 GPIO15,16,17,19
    fpioa.set_function(15, FPIOA.GPIO15)
    fpioa.set_function(16, FPIOA.GPIO16)
    fpioa.set_function(17, FPIOA.GPIO17)
    fpioa.set_function(19, FPIOA.GPIO19)
    
    # Y轴步进电机 GPIO27,14,61,40
    fpioa.set_function(27, FPIOA.GPIO27)
    fpioa.set_function(14, FPIOA.GPIO14)
    fpioa.set_function(61, FPIOA.GPIO61)
    fpioa.set_function(40, FPIOA.GPIO40)
    
    motor_pins = [
        Pin(15, Pin.OUT), Pin(16, Pin.OUT), Pin(17, Pin.OUT), Pin(19, Pin.OUT),  # X轴
        Pin(27, Pin.OUT), Pin(14, Pin.OUT), Pin(61, Pin.OUT), Pin(40, Pin.OUT)   # Y轴
    ]
    
    # 步进电机定时器
    motor_timer = Timer(-1)
    motor_timer.init(period=5, mode=Timer.PERIODIC, callback=motor_callback)
    
    # 触摸屏
    tp = TOUCH(0)
    
    print("硬件初始化完成")

def motor_callback(arg):
    """步进电机控制回调"""
    global step_num, motor_x_dir, motor_y_dir, motor_pins
    
    # X轴控制
    if motor_x_dir != 0:
        if motor_x_dir == 1:
            step_num = (step_num + 1) % 4
        else:
            step_num = (step_num - 1 + 4) % 4
        
        step_status = step_status_lst[step_num]
        for i in range(4):
            motor_pins[i].value(step_status[i])
    else:
        for i in range(4):
            motor_pins[i].value(0)
    
    # Y轴控制
    if motor_y_dir != 0:
        step_status = step_status_lst[step_num]
        for i in range(4, 8):
            motor_pins[i].value(step_status[i-4] if motor_y_dir == 1 else step_status[3-(i-4)])
    else:
        for i in range(4, 8):
            motor_pins[i].value(0)

def detect_rectangle(img):
    """检测矩形"""
    global threshold_rect
    
    img_gray = img.to_grayscale(copy=True)
    img_binary = img_gray.binary(threshold_rect)
    rects = img_binary.find_rects(threshold=8000)
    
    if rects:
        # 选择最大矩形
        largest = max(rects, key=lambda r: r.w() * r.h())
        corners = largest.corners()
        
        # 绘制矩形
        for i in range(4):
            next_i = (i + 1) % 4
            img.draw_line(corners[i][0], corners[i][1], 
                         corners[next_i][0], corners[next_i][1], 
                         color=(0, 255, 0), thickness=3)
        
        # 计算中心
        center_x = sum([c[0] for c in corners]) // 4
        center_y = sum([c[1] for c in corners]) // 4
        
        img.draw_circle(center_x, center_y, 8, color=(255, 0, 0), thickness=2, fill=True)
        
        return center_x, center_y, True
    
    return 0, 0, False

def control_aim(rect_x, rect_y):
    """控制瞄准"""
    global motor_x_dir, motor_y_dir, laser_pin, aim_success_time
    
    center_x, center_y = 400, 240
    error_x = rect_x - center_x
    error_y = rect_y - center_y
    dead_zone = 15
    
    # 控制电机
    motor_x_dir = 1 if error_x > dead_zone else (-1 if error_x < -dead_zone else 0)
    motor_y_dir = 1 if error_y > dead_zone else (-1 if error_y < -dead_zone else 0)
    
    # 瞄准成功判断
    if abs(error_x) <= dead_zone and abs(error_y) <= dead_zone:
        if aim_success_time == 0:
            aim_success_time = time.ticks_ms()
        
        # 开启激光4秒
        if time.ticks_diff(time.ticks_ms(), aim_success_time) < laser_on_duration:
            laser_pin.value(1)
            return "激光发射中"
        else:
            laser_pin.value(0)
            return "发射完成"
    else:
        aim_success_time = 0
        laser_pin.value(0)
        return "瞄准中"

try:
    print("基本要求第2问启动...")
    
    # 初始化传感器
    sensor = Sensor(width=800, height=480)
    sensor.reset()
    sensor.set_hmirror(True)  # 镜像
    sensor.set_vflip(True)    # 翻转
    sensor.set_framesize(width=800, height=480)
    sensor.set_pixformat(Sensor.RGB565)
    
    Display.init(Display.LT9611, width=800, height=480, to_ide=True)
    MediaManager.init()
    sensor.run()
    
    init_hardware()
    
    clock = time.clock()
    touch_counter = 0
    
    print("系统就绪 - 将自动识别矩形并瞄准")
    
    while True:
        clock.tick()
        os.exitpoint()
        
        img = sensor.snapshot()
        
        # 检测矩形
        rect_x, rect_y, found = detect_rectangle(img)
        
        if found:
            # 控制瞄准
            status = control_aim(rect_x, rect_y)
            
            # 显示信息
            img.draw_string_advanced(10, 10, 30, f"矩形: ({rect_x}, {rect_y})", color=(255, 255, 0))
            img.draw_string_advanced(10, 50, 30, f"状态: {status}", color=(0, 255, 0))
            
            # 显示偏差
            error_x = rect_x - 400
            error_y = rect_y - 240
            img.draw_string_advanced(10, 90, 25, f"偏差: X={error_x} Y={error_y}", color=(255, 255, 255))
            
        else:
            # 停止所有动作
            motor_x_dir = 0
            motor_y_dir = 0
            laser_pin.value(0)
            aim_success_time = 0
            img.draw_string_advanced(10, 10, 30, "未检测到矩形", color=(255, 0, 0))
        
        # 系统状态
        img.draw_string_advanced(10, 130, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
        img.draw_string_advanced(10, 160, 25, f"激光: {'开' if laser_pin.value() else '关'}", color=(255, 255, 255))
        img.draw_string_advanced(10, 190, 25, f"电机: X={motor_x_dir} Y={motor_y_dir}", color=(255, 255, 255))
        
        # 瞄准十字线
        img.draw_line(380, 240, 420, 240, color=(255, 0, 255), thickness=2)
        img.draw_line(400, 220, 400, 260, color=(255, 0, 255), thickness=2)
        
        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt:
    print("用户停止")
except Exception as e:
    print(f"异常: {e}")
finally:
    # 清理
    if motor_timer:
        motor_timer.deinit()
    if laser_pin:
        laser_pin.value(0)
    if motor_pins:
        for pin in motor_pins:
            pin.value(0)
    if tp:
        tp.deinit()
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("系统关闭")

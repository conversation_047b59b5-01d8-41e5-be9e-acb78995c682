# CanMV K230 API 文档

## 概述

本文档基于嘉楠科技 CanMV K230 开发板的官方API手册整理，涵盖了 Python 标准库、MicroPython 特有库、图像多媒体、AI 相关模块等完整的API参考。

## 目录结构

### 1. Python 标准库和 MicroPython 标准微库

#### 1.1 Ucryptolib 模块

**概述**

`Ucryptolib` 库提供了以下加解密功能：AES-ECB、AES-CBC、AES-CTR、AES-GCM 以及 SM4。其中，AES-ECB、AES-CBC 和 AES-CTR 模式由 MicroPython 的软件库原生实现，而 AES-GCM 和 SM4 则通过底层硬件加速器进行加速。

**注意**：本文档不详细介绍 AES-ECB、AES-CBC 和 AES-CTR 模式的加解密步骤，相关信息请参考 MicroPython 的 [cryptolib 官方文档](https://docs.micropython.org/en/latest/library/cryptolib.html)。

**API 介绍**

`Ucryptolib` 库提供了两个主要类：`aes` 和 `sm4`，这两个类分别实现了加密 (`encrypt`) 和解密 (`decrypt`) 操作。

##### 类 `aes`

**描述**

类 `aes` 用于初始化一个 AES-GCM 加解密对象，支持执行加密和解密操作。在 AES-GCM 加解密算法中，初始化时必须指定密钥 (`key`)、模式 (`mode`)、初始化向量 (`IV`) 和附加认证数据 (`AAD`)。

**注意**：初始化后，加密或解密对象只能用于单一操作，即加密或解密，不支持同时用于两者。

**语法**

```python
ucryptolib.aes((key, mode, IV, AAD))
```

**参数**

| 参数名称 | 描述 | 输入/输出 |
|---------|------|----------|
| key | 加解密密钥，支持长度为 256 比特的密钥 | 输入 |
| mode | 加解密模式，支持的模式为 AES-GCM，设置 `mode=0` 即可 | 输入 |
| IV | 初始化向量，长度为 12 字节 | 输入 |
| AAD | 附加认证数据，用于验证数据完整性，支持的长度为任意 | 输入 |

**示例**

```python
import ucryptolib

# 初始化AES-GCM对象
cipher = ucryptolib.aes(key, mode=0, IV=iv, AAD=aad)

# 执行加密操作
ciphertext = cipher.encrypt(plaintext)

# 执行解密操作
decrypted_text = cipher.decrypt(ciphertext)
```

##### 类 `sm4`

**描述**

类 `sm4` 用于初始化一个 SM4 加解密对象，支持中国国家密码算法标准 SM4 的加密和解密操作。与 AES-GCM 相似，SM4 初始化时需要提供密钥 (`key`)、模式 (`mode`) 和初始化向量 (`IV`)。

**语法**

```python
ucryptolib.sm4((key, mode, IV))
```

**参数**

| 参数名称 | 描述 | 输入/输出 |
|---------|------|----------|
| key | SM4 加解密密钥，支持的密钥长度为 128 比特 | 输入 |
| mode | 加解密模式，支持 ECB（电子密码本）和 CBC（加密块链）模式 | 输入 |
| IV | 初始化向量，长度为 16 字节，CBC 模式下必须提供 | 输入 |

**示例**

```python
import ucryptolib

# 初始化SM4对象
cipher = ucryptolib.sm4(key, mode=1, IV=iv)

# 执行加密操作
ciphertext = cipher.encrypt(plaintext)

# 执行解密操作
decrypted_text = cipher.decrypt(ciphertext)
```

#### 1.2 Hashlib 模块

**概述**

`uhashlib` 库提供了基于 MD5、SHA1 和 SHA2 算法的二进制数据哈希功能。

**API 介绍**

`uhashlib` 库提供了三个类：`md5`、`sha1` 和 `sha256`。这些类分别实现了两个函数：数据更新函数 `update()` 和消息摘要函数 `digest()`。其中，`md5` 和 `sha1` 是 MicroPython 的软件实现；`sha256` 则由底层硬件加速器加速。

##### 类 `sha256`

**描述**

`sha256` 类用于创建一个 SHA256 哈希对象，并可选择性地向其中发送数据。

**语法**

```python
uhashlib.sha256([data])
```

**参数**

| 参数名称 | 描述 | 输入/输出 |
|---------|------|----------|
| data (可选) | 二进制数据 | 输入 |

**示例**

```python
import uhashlib
import binascii

# 初始化 sha256 对象
obj = uhashlib.sha256()
# 输入数据1
obj.update(b'hello')
# 输入数据2
obj.update(b'world')
# 计算摘要
dgst = obj.digest()
print(binascii.hexlify(dgst))
# b'936a185caaa266bb9cbe981e9e05cb78cd732b0b3280eb944412bb6f8f8f07af'
```

**方法**

- **`update(data)`**：更新哈希对象的数据
- **`digest()`**：返回所有输入数据的哈希值（只能调用一次）
- **`hexdigest()`**：该方法未实现，可使用 `binascii.hexlify(hash.digest())` 达到类似效果

#### 1.3 utime 模块

**概述**

`utime` 模块提供获取当前时间与日期、测量时间间隔以及延迟操作的相关功能。

**主要函数**

- **`ntp_sync()`**：从互联网同步当前时间
- **`localtime([secs])`**：将时间转换为8元组格式
- **`mktime(tuple)`**：`localtime()` 的逆函数
- **`sleep(seconds)`**：延迟执行指定秒数
- **`sleep_ms(ms)`**：延迟指定毫秒数
- **`sleep_us(us)`**：延迟指定微秒数
- **`ticks_ms()`**：返回毫秒计数器
- **`ticks_us()`**：返回微秒计数器
- **`ticks_cpu()`**：提供最高分辨率计数器
- **`ticks_add(ticks, delta)`**：计算新的 ticks 值
- **`ticks_diff(ticks1, ticks2)`**：计算两个 ticks 值的差异
- **`time()`**：返回自纪元以来的秒数
- **`clock()`**：返回一个 `clock` 对象用于时间测量

**clock 类**

```python
import utime
clock = utime.clock()
while True:
    clock.tick()
    utime.sleep(0.1)
    print("fps = ", clock.fps())
```

**方法**：
- **`tick()`**：记录当前时间
- **`fps()`**：计算帧率
- **`reset()`**：重置所有计时标记
- **`avg()`**：计算每帧的平均时间消耗

#### 1.4 其他标准库模块

- **gc 模块**：内存管理
- **uos 模块**：基本操作系统服务

### 2. MicroPython 特有库

#### 2.1 网络通信模块

- **uctypes 模块**：类型转换功能
- **network 模块**：网络连接管理
- **socket 模块**：套接字通信

#### 2.2 硬件控制模块

##### Pin 模块

**概述**

K230 芯片内部包含 64 个 GPIO（通用输入输出）引脚，每个引脚均可配置为输入或输出模式，并支持上下拉电阻配置和驱动能力设置。

**构造函数**

```python
from machine import Pin

# 将引脚 2 配置为输出模式，无上下拉，驱动能力为 7
pin = Pin(2, Pin.OUT, pull=Pin.PULL_NONE, drive=7)
```

**参数**

| 参数 | 描述 | 范围 |
|------|------|------|
| index | 引脚编号 | [0, 63] |
| mode | 引脚模式 | Pin.IN / Pin.OUT |
| pull | 上下拉配置 | Pin.PULL_NONE / Pin.PULL_UP / Pin.PULL_DOWN |
| drive | 驱动能力 | 0-15 |
| value | 默认输出值 | 0/1 |

**主要方法**

- **`init(mode, pull, drive)`**：初始化引脚配置
- **`value([value])`**：获取或设置引脚电平
- **`mode([mode])`**：获取或设置引脚模式
- **`pull([pull])`**：获取或设置上下拉配置
- **`drive([drive])`**：获取或设置驱动能力
- **`on()` / `high()`**：设置高电平
- **`off()` / `low()`**：设置低电平
- **`irq(handler, trigger, debounce=10)`**：配置中断

**中断触发模式**

- **IRQ_FALLING**：下降沿触发
- **IRQ_RISING**：上升沿触发
- **IRQ_LOW_LEVEL**：低电平触发
- **IRQ_HIGH_LEVEL**：高电平触发
- **IRQ_BOTH**：边沿触发

**示例**

```python
from machine import Pin

# 配置输出引脚
led = Pin(2, Pin.OUT)
led.on()  # 点亮LED

# 配置输入引脚
button = Pin(3, Pin.IN, pull=Pin.PULL_UP)
if button.value() == 0:
    print("按钮被按下")

# 配置中断
def button_handler(pin):
    print("按钮中断触发")

button.irq(handler=button_handler, trigger=Pin.IRQ_FALLING)
```

##### FPIOA 模块

**概述**

FPIOA（Field Programmable IO Array）是 K230 芯片的可编程 IO 阵列，允许用户将芯片内部的功能模块映射到任意的 IO 引脚上，实现灵活的引脚复用功能。

**主要功能**

- 支持 64 个 GPIO 引脚的功能配置
- 可将内部功能模块映射到任意引脚
- 支持引脚驱动能力、上下拉、施密特触发器等配置
- 提供引脚状态查询功能

**构造函数**

```python
from machine import FPIOA

# 创建 FPIOA 对象
fpioa = FPIOA()
```

**主要方法**

- **`set_function(pin, func)`**：设置引脚功能
- **`get_pin_func(pin)`**：获取引脚当前功能
- **`get_pin_num(func)`**：获取功能对应的引脚号
- **`set_pin_attribute(pin, **kwargs)`**：设置引脚属性
- **`get_pin_attribute(pin)`**：获取引脚属性

**功能常量**

```python
# 常用功能定义
FPIOA.GPIO0 - FPIOA.GPIO63    # GPIO 功能
FPIOA.UART0_TXD               # UART0 发送
FPIOA.UART0_RXD               # UART0 接收
FPIOA.I2C0_SDA                # I2C0 数据线
FPIOA.I2C0_SCL                # I2C0 时钟线
FPIOA.SPI0_CLK                # SPI0 时钟
FPIOA.SPI0_MOSI               # SPI0 主出从入
FPIOA.SPI0_MISO               # SPI0 主入从出
FPIOA.PWM0                    # PWM 输出
```

**示例**

```python
from machine import FPIOA, Pin, UART

# 配置引脚功能
fpioa = FPIOA()

# 将引脚 0 配置为 UART0 TXD
fpioa.set_function(0, FPIOA.UART0_TXD)
# 将引脚 1 配置为 UART0 RXD
fpioa.set_function(1, FPIOA.UART0_RXD)

# 设置引脚属性
fpioa.set_pin_attribute(0, pull=Pin.PULL_UP, drive=7)

# 查询引脚功能
func = fpioa.get_pin_func(0)
print(f"引脚 0 的功能: {func}")

# 初始化 UART
uart = UART(0, 115200)
uart.write("Hello World!")
```

##### UART 模块

**概述**

UART（Universal Asynchronous Receiver/Transmitter）通用异步收发器，用于串行通信。K230 支持多个 UART 接口，可用于与其他设备进行串行数据传输。

**构造函数**

```python
from machine import UART

# 基本初始化
uart = UART(id, baudrate=115200, bits=8, parity=None, stop=1, timeout=1000, read_buf_len=64)
```

**参数**

| 参数 | 描述 | 默认值 |
|------|------|--------|
| id | UART 设备号 (0-4) | 必需 |
| baudrate | 波特率 | 115200 |
| bits | 数据位数 (5-8) | 8 |
| parity | 奇偶校验 (None/0/1) | None |
| stop | 停止位 (1-2) | 1 |
| timeout | 超时时间 (ms) | 1000 |
| read_buf_len | 接收缓冲区大小 | 64 |

**主要方法**

- **`init(baudrate, bits, parity, stop, timeout, read_buf_len)`**：重新初始化
- **`deinit()`**：反初始化，释放资源
- **`any()`**：返回接收缓冲区中的字节数
- **`read([nbytes])`**：读取数据
- **`readline()`**：读取一行数据
- **`readinto(buf[, nbytes])`**：读取数据到缓冲区
- **`write(buf)`**：写入数据
- **`sendbreak()`**：发送中断信号

**示例**

```python
from machine import UART, FPIOA

# 配置引脚
fpioa = FPIOA()
fpioa.set_function(0, FPIOA.UART0_TXD)
fpioa.set_function(1, FPIOA.UART0_RXD)

# 初始化 UART
uart = UART(0, baudrate=115200, bits=8, parity=None, stop=1)

# 发送数据
uart.write("Hello, UART!\n")

# 接收数据
if uart.any():
    data = uart.read()
    print("接收到:", data)

# 发送和接收字符串
uart.write("测试中文\n")
response = uart.readline()
print("响应:", response.decode('utf-8'))

# 关闭 UART
uart.deinit()
```

##### machine 模块

**概述**

`machine` 模块包含与特定硬件板相关的功能，提供对系统硬件组件的直接访问和控制。

**主要功能**

- 系统复位和引导控制
- 内存操作
- 硬件信息获取
- 温度监测

**核心方法**

- **`reset()`**：立即复位 SoC（系统级芯片）
- **`bootloader()`**：进入引导加载程序
- **`mem_copy(dst, src, size)`**：内存复制操作
- **`temperature()`**：获取芯片内部温度
- **`chipid()`**：获取芯片 ID

**示例**

```python
import machine
import utime

# 获取芯片温度
temp = machine.temperature()
print(f"芯片温度: {temp}°C")

# 获取芯片 ID
chip_id = machine.chipid()
print(f"芯片 ID: {chip_id.hex()}")

# 内存复制示例（谨慎使用）
# machine.mem_copy(dst_addr, src_addr, size)

# 延时后重启系统
print("5秒后重启系统...")
utime.sleep(5)
machine.reset()
```

##### TOUCH 模块

**概述**

TOUCH 模块基于 RTT 的触摸框架，支持单点和多点电容触摸屏及电阻触摸屏，提供完整的触摸检测和处理功能。

**构造函数**

```python
from machine import TOUCH, I2C, Pin

# 系统自带触摸
touch = TOUCH(0, type=TOUCH.TYPE_CST328, rotate=-1)

# 自定义触摸驱动
touch = TOUCH(1, type=TOUCH.TYPE_CST328, rotate=TOUCH.ROTATE_0,
              range_x=800, range_y=480, i2c=i2c_obj,
              slave_addr=0x1A, rst=rst_pin)
```

**参数**

| 参数 | 描述 | 适用场景 |
|------|------|----------|
| index | 设备号 (0/1) | 0=系统自带，1=自定义 |
| type | 触摸驱动类型 | TYPE_CST328/TYPE_CST226SE/TYPE_GT911 |
| rotate | 坐标旋转 | ROTATE_0/90/180/270 |
| range_x | 触摸宽度最大值 | index=1 时有效 |
| range_y | 触摸高度最大值 | index=1 时有效 |
| i2c | I2C 总线对象 | index=1 时有效 |
| slave_addr | 从机地址 | index=1 时可选 |
| rst | 复位引脚 | index=1 时有效 |

**主要方法**

- **`read([count])`**：获取触摸数据
- **`deinit()`**：释放资源

**TOUCH_INFO 类**

触摸点信息类，包含以下只读属性：

- **`event`**：事件码（EVENT_NONE/UP/DOWN/MOVE）
- **`track_id`**：触点 ID（多点触摸）
- **`width`**：触点宽度
- **`x`**：触点 x 坐标
- **`y`**：触点 y 坐标
- **`timestamp`**：触点时间戳

**示例**

```python
from machine import TOUCH
import utime

# 初始化触摸屏
touch = TOUCH(0)

print("触摸屏已初始化，开始检测触摸...")

try:
    while True:
        # 读取触摸数据
        touch_points = touch.read()

        if touch_points:
            for point in touch_points:
                if point.event == TOUCH.EVENT_DOWN:
                    print(f"触摸按下: ({point.x}, {point.y})")
                elif point.event == TOUCH.EVENT_MOVE:
                    print(f"触摸移动: ({point.x}, {point.y})")
                elif point.event == TOUCH.EVENT_UP:
                    print(f"触摸抬起: ({point.x}, {point.y})")

        utime.sleep_ms(50)

except KeyboardInterrupt:
    print("停止触摸检测")
finally:
    touch.deinit()
```

##### 其他硬件控制模块

- **ADC 模块**：模数转换器
- **FFT 模块**：快速傅里叶变换
- **I2C 模块**：I2C 通信
- **PWM 模块**：脉宽调制
- **SPI 模块**：SPI 通信
- **Timer 模块**：定时器
- **WDT 模块**：看门狗定时器
- **RTC 模块**：实时时钟
- **LED 模块**：LED 控制
- **SPI_LCD 模块**：SPI LCD 显示屏
- **USB Serial 模块**：USB 串口

### 3. 图像多媒体模块

#### 3.1 图像处理

##### Sensor 模块

**概述**

CanMV K230 平台的 `sensor` 模块负责图像采集与数据处理。该模块提供了一套高级 API，开发者可以利用这些接口轻松获取不同格式与尺寸的图像。

**架构特点**

- 最多支持三路图像传感器同时接入
- 每个视频通道可并行输出三路图像数据
- 支持多种分辨率和像素格式
- 硬件加速的图像处理能力

**构造函数**

```python
# 基本初始化
sensor = Sensor(id=0)

# 指定分辨率和帧率
sensor = Sensor(id=0, width=1280, height=720, fps=60)
sensor = Sensor(id=0, width=640, height=480)
```

**参数**

| 参数 | 描述 | 默认值 |
|------|------|--------|
| id | CSI 端口号 (0-2) | 根据开发板而定 |
| width | 最大输出图像宽度 | 1920 |
| height | 最大输出图像高度 | 1080 |
| fps | 最大输出帧率 | 30 |

**主要方法**

- **`reset()`**：复位传感器（必须调用）
- **`set_framesize(framesize, chn, width, height)`**：设置输出图像尺寸
- **`set_pixformat(pix_format, chn)`**：设置像素格式
- **`set_hmirror(enable)`**：设置水平镜像
- **`set_vflip(enable)`**：设置垂直翻转
- **`run()`**：启动传感器输出
- **`stop()`**：停止传感器输出
- **`snapshot(chn, timeout, dump_frame)`**：捕获一帧图像
- **`bind_info(x, y, chn)`**：获取绑定信息

**像素格式支持**

| 格式 | 描述 |
|------|------|
| RGB565 | 16 位 RGB 格式 |
| RGB888 | 24 位 RGB 格式 |
| RGBP888 | 分离的 24 位 RGB |
| YUV420SP | 半平面 YUV |
| GRAYSCALE | 灰度图 |

**支持的传感器**

| 型号 | 最大分辨率 | 最大帧率 |
|------|------------|----------|
| OV5647 | 2592x1944 | 10 FPS |
|        | 1920x1080 | 30 FPS |
|        | 640x480 | 90 FPS |
| GC2093 | 1920x1080 | 60 FPS |
|        | 1280x720 | 90 FPS |
| IMX335 | 2592x1944 | 30 FPS |
|        | 1920x1080 | 30 FPS |

**示例**

```python
from media.sensor import *
from media.media import *

# 初始化传感器
sensor = Sensor(id=0)
sensor.reset()

# 配置输出
sensor.set_framesize(width=640, height=480, chn=CAM_CHN_ID_0)
sensor.set_pixformat(sensor.RGB888, chn=CAM_CHN_ID_0)

# 启动传感器
MediaManager.init()
sensor.run()

# 捕获图像
while True:
    img = sensor.snapshot()
    if img:
        # 处理图像
        print("捕获到图像:", img.width(), "x", img.height())

# 停止传感器
sensor.stop()
MediaManager.deinit()
```

##### Display 模块

**概述**

Display 模块用于控制各种显示设备，包括 LCD、HDMI 等显示接口。该模块提供了完整的显示管理功能，支持多层显示合成、硬件加速渲染等特性。

**支持的显示设备**

| 设备类型 | 默认分辨率 | 支持分辨率 |
|----------|------------|------------|
| VIRT | 640x480@90 | 自定义 (64x64)-(4096x4096) |
| ST7701 | 800x480 | 800x480, 480x800, 854x480, 640x480 等 |
| HX8399 | 1920x1080 | 1920x1080, 1080x1920 |
| ILI9806 | 800x480 | 800x480, 480x800 |
| ILI9881 | 1280x800 | 1280x800, 800x1280 |
| LT9611 | 1920x1080@30 | 支持多种分辨率和帧率 |

**主要方法**

- **`init(type, width, height, osd_num, to_ide, fps, quality)`**：初始化显示通路
- **`show_image(img, x, y, layer, alpha, flag)`**：显示图像
- **`bind_layer(src, dstlayer, rect, pix_format, alpha, flag)`**：绑定显示层
- **`width([layer])`**：获取显示宽度
- **`height([layer])`**：获取显示高度
- **`deinit()`**：反初始化显示

**显示层支持**

| 显示层 | 说明 | 支持功能 |
|--------|------|----------|
| LAYER_VIDEO1 | 视频层1 | bind_layer，支持硬件旋转 |
| LAYER_VIDEO2 | 视频层2 | bind_layer，不支持硬件旋转 |
| LAYER_OSD0-3 | OSD层0-3 | show_image 和 bind_layer |

**旋转和镜像标志**

- **FLAG_ROTATION_0/90/180/270**：旋转角度
- **FLAG_MIRROR_NONE/HOR/VER/BOTH**：镜像方式

**示例**

```python
from media.display import *
from media.media import *
import image

# 初始化显示
Display.init(Display.ST7701, width=800, height=480, to_ide=True)
MediaManager.init()

# 创建图像并显示
img = image.Image(800, 480, image.RGB565)
img.clear()
img.draw_string_advanced(0, 0, 32, "Hello Display!", color=(255, 0, 0))

# 显示图像
Display.show_image(img)

# 获取显示信息
width = Display.width()
height = Display.height()
print(f"显示分辨率: {width}x{height}")

# 清理资源
Display.deinit()
MediaManager.deinit()
```

##### 图像处理模块

**概述**

图像处理模块移植自 OpenMV，提供了丰富的计算机视觉和图像处理功能。支持多种图像格式和内存分配方式，兼容 OpenMV 的大部分 API。

**支持的图像格式**

- **BINARY**：二值图像
- **GRAYSCALE**：灰度图像
- **RGB565**：16位RGB图像
- **BAYER**：拜耳格式
- **YUV422**：YUV422格式
- **JPEG**：JPEG压缩格式
- **PNG**：PNG格式
- **ARGB8888**：32位ARGB（新增）
- **RGB888**：24位RGB（新增）
- **RGBP888**：分离的24位RGB（新增）
- **YUV420**：YUV420格式（新增）

**内存分配方式**

- **ALLOC_MPGC**：MicroPython 管理的内存
- **ALLOC_HEAP**：系统堆内存
- **ALLOC_MMZ**：多媒体内存（默认）
- **ALLOC_VB**：视频缓冲区
- **ALLOC_REF**：引用外部内存

**Image 类核心方法**

**构造和转换**

```python
import image

# 创建图像
img = image.Image(640, 480, image.RGB888)
img = image.Image("/sdcard/test.jpg")

# 格式转换
rgb_img = img.to_rgb888()
gray_img = img.to_grayscale()
rgb565_img = img.to_rgb565()

# 内存操作
img.copy_from(src_img)
img.copy_to(dst_img)
numpy_array = img.to_numpy_ref()
```

**绘图功能**

```python
# 基本绘图
img.clear()
img.draw_line(0, 0, 100, 100, color=(255, 0, 0))
img.draw_rectangle(10, 10, 50, 50, color=(0, 255, 0))
img.draw_circle(50, 50, 25, color=(0, 0, 255))
img.draw_string(10, 10, "Hello", color=(255, 255, 255))

# 高级绘图
img.draw_string_advanced(0, 0, 32, "中文支持", color=(255, 0, 0))
img.draw_arrow(0, 0, 100, 100)
img.draw_cross(50, 50, size=10)
```

**图像算法**

```python
# 颜色识别
blobs = img.find_blobs([(20, 100, 15, 127, 15, 127)])
for blob in blobs:
    img.draw_rectangle(blob.rect(), color=(255, 0, 0))

# 二维码识别
qrcodes = img.find_qrcodes()
for qr in qrcodes:
    print("二维码内容:", qr.payload())
    img.draw_rectangle(qr.rect(), color=(0, 255, 0))

# 条形码识别
barcodes = img.find_barcodes()
for barcode in barcodes:
    print("条形码:", barcode.payload())

# AprilTag 识别
apriltags = img.find_apriltags()
for tag in apriltags:
    print(f"AprilTag ID: {tag.id()}")
    img.draw_rectangle(tag.rect(), color=(255, 255, 0))

# 直线检测
lines = img.find_lines()
for line in lines:
    img.draw_line(line.line(), color=(0, 255, 255))

# 圆形检测
circles = img.find_circles()
for circle in circles:
    img.draw_circle(circle.x(), circle.y(), circle.r(), color=(255, 0, 255))
```

**图像滤波和处理**

```python
# 基本滤波
img.gaussian(1)          # 高斯滤波
img.median(1)            # 中值滤波
img.bilateral(3)         # 双边滤波

# 形态学操作
img.erode(1)             # 腐蚀
img.dilate(1)            # 膨胀
img.open(1)              # 开运算
img.close(1)             # 闭运算

# 图像运算
img.add(other_img)       # 图像相加
img.sub(other_img)       # 图像相减
img.mul(other_img)       # 图像相乘
img.blend(other_img, alpha=128)  # 图像混合

# 二值化
img.binary([(100, 255)])  # 二值化
img.invert()             # 反转
```

**统计和分析**

```python
# 获取统计信息
stats = img.get_statistics()
print(f"平均值: {stats.mean()}")
print(f"标准差: {stats.stdev()}")

# 获取直方图
hist = img.get_histogram()
print(f"直方图: {hist.bins()}")

# 获取相似度
similarity = img.get_similarity(other_img)
print(f"相似度: {similarity.mean()}")
```

**完整示例**

```python
import image
from media.sensor import *
from media.display import *
from media.media import *

# 初始化
sensor = Sensor(id=0)
sensor.reset()
sensor.set_framesize(width=640, height=480)
sensor.set_pixformat(sensor.RGB888)

Display.init(Display.ST7701, width=800, height=480)
MediaManager.init()
sensor.run()

try:
    while True:
        # 捕获图像
        img = sensor.snapshot()
        if img:
            # 图像处理
            blobs = img.find_blobs([(0, 100, -128, 127, -128, 127)])

            # 绘制结果
            for blob in blobs:
                img.draw_rectangle(blob.rect(), color=(255, 0, 0))
                img.draw_cross(blob.cx(), blob.cy(), color=(0, 255, 0))

            # 显示图像
            Display.show_image(img)

except KeyboardInterrupt:
    print("停止图像处理")
finally:
    sensor.stop()
    Display.deinit()
    MediaManager.deinit()
```

#### 3.2 音视频处理

##### 音频模块

**功能特点**

- 支持音频录制和播放
- 多种音频格式支持
- 音频流处理
- 音量控制和音效处理

##### 视频编解码模块

**VDEC 模块（视频解码）**

- 硬件加速视频解码
- 支持多种视频格式（H.264、H.265等）
- 高性能解码能力

**VENC 模块（视频编码）**

- 硬件加速视频编码
- 实时视频编码
- 可配置编码参数

**MP4 模块**

- MP4 文件读写
- 视频容器格式处理
- 元数据管理

##### 流媒体模块

**RTSP 模块**

- RTSP 服务器功能
- 实时流媒体传输
- 网络视频流分发

**UVC 模块**

- USB 视频类设备支持
- USB 摄像头接口
- 即插即用功能

#### 3.3 显示和界面

##### Display 模块

**功能特点**

- 多种显示接口支持（HDMI、MIPI-DSI、SPI等）
- 多层显示合成
- 硬件加速图形渲染
- 显示缓冲管理

##### LVGL 模块

**概述**

LVGL（Light and Versatile Graphics Library）是一个轻量级的图形库，为嵌入式系统提供丰富的GUI功能。

**主要特性**

- 丰富的控件库（按钮、标签、图表等）
- 主题和样式系统
- 动画效果支持
- 触摸屏交互
- 多语言支持

**基本使用**

```python
import lvgl as lv

# 创建屏幕
scr = lv.obj()

# 创建按钮
btn = lv.btn(scr)
btn.set_size(100, 50)
btn.center()

# 添加标签
label = lv.label(btn)
label.set_text("按钮")

# 加载屏幕
lv.scr_load(scr)
```

#### 3.4 系统管理

##### PM 模块（电源管理）

**功能特点**

- CPU 频率调节
- 电源模式切换
- 低功耗管理
- 系统休眠和唤醒

##### NONAI2D CSC 模块

**功能特点**

- 颜色空间转换
- 图像格式转换
- 硬件加速处理
- 高效的像素格式转换

### 4. AI 相关模块

#### 4.1 nncase_runtime 模块

**概述**

`nncase_runtime` 模块是 K230 平台的神经网络推理运行时，包含 KPU（神经网络处理单元）和 AI2D 模块，用于实现高效的深度学习推理和图像处理功能。

**主要组件**

- **nncase_runtime.kpu**：KPU 模块，用于神经网络模型推理
- **nncase_runtime.ai2d**：AI2D 模块，用于图像预处理

##### KPU 模块

**主要功能**

- 加载和运行 kmodel 格式的神经网络模型
- 管理输入输出张量
- 执行神经网络推理

**核心方法**

```python
import nncase_runtime as nn

# 创建 KPU 对象
kpu = nn.kpu()

# 加载模型
kpu.load_kmodel("/path/to/model.kmodel")

# 设置输入
input_tensor = nn.from_numpy(input_data)
kpu.set_input_tensor(0, input_tensor)

# 执行推理
kpu.run()

# 获取输出
output_tensor = kpu.get_output_tensor(0)
result = output_tensor.to_numpy()
```

**API 列表**

- **`load_kmodel(path)`**：加载 kmodel 文件
- **`set_input_tensor(index, tensor)`**：设置输入张量
- **`get_input_tensor(index)`**：获取输入张量
- **`set_output_tensor(index, tensor)`**：设置输出张量
- **`get_output_tensor(index)`**：获取输出张量
- **`run()`**：执行推理
- **`inputs_size()`**：获取输入数量
- **`outputs_size()`**：获取输出数量
- **`get_input_desc(index)`**：获取输入描述信息
- **`get_output_desc(index)`**：获取输出描述信息

##### AI2D 模块

**主要功能**

- 图像格式转换
- 图像裁剪、缩放、仿射变换
- 数据类型转换
- 图像预处理流水线

**核心方法**

```python
import nncase_runtime as nn
import ulab.numpy as np

# 创建 AI2D 对象
ai2d = nn.ai2d()

# 设置数据类型
ai2d.set_dtype(nn.ai2d_format.NCHW_FMT, nn.ai2d_format.NCHW_FMT,
               np.uint8, np.uint8)

# 设置缩放参数
ai2d.set_resize_param(True, nn.interp_method.tf_bilinear,
                      nn.interp_mode.half_pixel)

# 构建处理器
ai2d_builder = ai2d.build([1,3,640,480], [1,3,320,320])

# 执行处理
ai2d_builder.run(input_tensor, output_tensor)
```

**配置方法**

- **`set_dtype(src_format, dst_format, src_type, dst_type)`**：设置数据类型
- **`set_crop_param(crop_flag, start_x, start_y, width, height)`**：设置裁剪参数
- **`set_shift_param(shift_flag, shift_val)`**：设置位移参数
- **`set_pad_param(pad_flag, paddings, pad_mode, pad_val)`**：设置填充参数
- **`set_resize_param(resize_flag, interp_method, interp_mode)`**：设置缩放参数
- **`set_affine_param(affine_flag, interp_method, ...)`**：设置仿射变换参数

##### 工具函数

- **`from_numpy(numpy_array)`**：从 numpy 数组创建 runtime_tensor
- **`to_numpy()`**：将 runtime_tensor 转换为 numpy 数组
- **`shrink_memory_pool()`**：清理内存池，释放内存

**完整示例**

```python
import nncase_runtime as nn
import ulab.numpy as np
import image

# 加载模型
kpu = nn.kpu()
kpu.load_kmodel("/sdcard/face_detection.kmodel")

# 读取图像
img = image.Image("/sdcard/test.jpg").to_rgb888()
img_data = img.to_numpy_ref()

# AI2D 预处理
ai2d = nn.ai2d()
ai2d.set_dtype(nn.ai2d_format.NCHW_FMT, nn.ai2d_format.NCHW_FMT,
               np.uint8, np.uint8)
ai2d.set_resize_param(True, nn.interp_method.tf_bilinear,
                      nn.interp_mode.half_pixel)

# 构建并运行预处理
ai2d_builder = ai2d.build([1,3,480,640], [1,3,320,320])
output_data = np.ones((1,3,320,320), dtype=np.uint8)
ai2d_output_tensor = nn.from_numpy(output_data)
ai2d_input_tensor = nn.from_numpy(img_data)
ai2d_builder.run(ai2d_input_tensor, ai2d_output_tensor)

# 模型推理
kpu.set_input_tensor(0, ai2d_output_tensor)
kpu.run()

# 获取结果
for i in range(kpu.outputs_size()):
    output_tensor = kpu.get_output_tensor(i)
    result = output_tensor.to_numpy()
    print("输出形状:", result.shape)
```

#### 4.2 AI Demo 库

##### PipeLine 模块

**功能特点**

- AI 处理流水线管理
- 多阶段处理协调
- 数据流控制
- 性能优化

##### AIBase 模块

**功能特点**

- AI 应用基础类
- 通用 AI 功能封装
- 模型管理接口
- 结果后处理

##### YOLO 模块

**功能特点**

- YOLO 目标检测算法
- 实时目标识别
- 边界框绘制
- 置信度过滤

**基本使用**

```python
from libs.YOLO import YOLO

# 初始化 YOLO 检测器
yolo = YOLO(model_path="/sdcard/yolo.kmodel")

# 检测目标
results = yolo.detect(image)
for result in results:
    print(f"类别: {result.class_name}, 置信度: {result.confidence}")
```

##### Utils 模块

**功能特点**

- AI 工具函数集合
- 图像处理辅助函数
- 数据转换工具
- 性能测试工具

## 快速参考

### 常用模块导入

```python
# 基础模块
import machine
from machine import Pin, I2C, SPI, UART, PWM, ADC, FPIOA, TOUCH
import utime
import gc
import uos

# 图像和传感器
from media.sensor import *
from media.display import *
from media.media import *
import image

# AI 相关
import nncase_runtime as nn
import ulab.numpy as np

# 网络通信
import network
import socket

# 加密和哈希
import ucryptolib
import uhashlib

# 图形界面
import lvgl as lv
```

### 常用操作示例

#### GPIO 控制

```python
from machine import Pin, FPIOA

# 配置引脚功能
fpioa = FPIOA()
fpioa.set_function(2, FPIOA.GPIO2)

# 输出控制
led = Pin(2, Pin.OUT)
led.on()   # 点亮
led.off()  # 熄灭

# 输入读取
button = Pin(3, Pin.IN, pull=Pin.PULL_UP)
if button.value() == 0:
    print("按钮按下")
```

#### 串口通信

```python
from machine import UART, FPIOA

# 配置引脚
fpioa = FPIOA()
fpioa.set_function(0, FPIOA.UART0_TXD)
fpioa.set_function(1, FPIOA.UART0_RXD)

# 初始化串口
uart = UART(0, baudrate=115200)
uart.write("Hello UART!\n")

# 读取数据
if uart.any():
    data = uart.read()
    print("接收:", data)
```

#### 触摸屏控制

```python
from machine import TOUCH

# 初始化触摸屏
touch = TOUCH(0)

# 读取触摸点
points = touch.read()
if points:
    for point in points:
        if point.event == TOUCH.EVENT_DOWN:
            print(f"触摸: ({point.x}, {point.y})")
```

#### 图像采集和显示

```python
from media.sensor import *
from media.display import *
from media.media import *

# 初始化显示
Display.init(Display.ST7701, width=800, height=480)

# 初始化传感器
sensor = Sensor(id=0)
sensor.reset()
sensor.set_framesize(width=640, height=480)
sensor.set_pixformat(sensor.RGB888)

# 启动系统
MediaManager.init()
sensor.run()

# 捕获并显示图像
img = sensor.snapshot()
Display.show_image(img)
```

#### 图像处理

```python
import image

# 创建图像
img = image.Image(640, 480, image.RGB888)

# 绘制图形
img.clear()
img.draw_string_advanced(0, 0, 32, "Hello 世界!", color=(255, 0, 0))
img.draw_rectangle(10, 10, 100, 50, color=(0, 255, 0))

# 颜色识别
blobs = img.find_blobs([(0, 100, -128, 127, -128, 127)])
for blob in blobs:
    img.draw_rectangle(blob.rect(), color=(255, 0, 0))

# 二维码识别
qrcodes = img.find_qrcodes()
for qr in qrcodes:
    print("二维码:", qr.payload())
```

#### AI 推理

```python
import nncase_runtime as nn

# 加载模型
kpu = nn.kpu()
kpu.load_kmodel("/sdcard/model.kmodel")

# 设置输入并推理
kpu.set_input_tensor(0, input_tensor)
kpu.run()

# 获取输出
output = kpu.get_output_tensor(0)
result = output.to_numpy()
```

### 模块分类索引

#### 硬件控制
- **Pin**：GPIO 引脚控制
- **FPIOA**：引脚功能配置
- **I2C**：I2C 通信
- **SPI**：SPI 通信
- **UART**：串口通信
- **PWM**：脉宽调制
- **ADC**：模数转换
- **Timer**：定时器
- **WDT**：看门狗
- **RTC**：实时时钟
- **TOUCH**：触摸屏控制

#### 图像视频
- **Sensor**：图像传感器
- **Display**：显示控制
- **Image**：图像处理（OpenMV兼容）
- **Audio**：音频处理
- **VENC/VDEC**：视频编解码
- **MP4**：视频文件处理
- **RTSP**：流媒体
- **LVGL**：图形界面库

#### AI 人工智能
- **nncase_runtime**：AI 推理引擎
- **KPU**：神经网络处理器
- **AI2D**：图像预处理
- **YOLO**：目标检测
- **PipeLine**：AI 处理流水线

#### 网络通信
- **network**：网络管理
- **socket**：套接字通信
- **HTTP**：HTTP 协议

#### 系统功能
- **machine**：硬件抽象层
- **utime**：时间管理
- **gc**：内存管理
- **uos**：操作系统接口
- **PM**：电源管理

#### 加密安全
- **ucryptolib**：加密算法（AES、SM4）
- **uhashlib**：哈希算法（MD5、SHA256）

## 开发资源

### 官方链接

- **文档中心**：[https://www.kendryte.com/zh/document](https://www.kendryte.com/zh/document)
- **资料下载**：[https://www.kendryte.com/zh/resource](https://www.kendryte.com/zh/resource)
- **模型库**：[https://www.kendryte.com/zh/model/library](https://www.kendryte.com/zh/model/library)
- **模型训练**：[https://www.kendryte.com/zh/training/start](https://www.kendryte.com/zh/training/start)

### 社区支持

- **问答社区**：[https://www.kendryte.com/answer](https://www.kendryte.com/answer)
- **QQ 群**：578895334
- **邮箱**：<EMAIL>

### GitHub 仓库

- **K230 CanMV 文档**：[https://github.com/kendryte/k230_canmv_docs](https://github.com/kendryte/k230_canmv_docs)
- **K230 文档**：[https://github.com/kendryte/k230_docs](https://github.com/kendryte/k230_docs)

## 版权信息

© Copyright 2025 嘉楠科技 | 京ICP备2025124317号 | 京公网安备11010802045870号

## 总结

本文档提供了 CanMV K230 开发板的完整 API 参考，涵盖了从基础硬件控制到高级 AI 应用的所有模块。主要特点包括：

### 🚀 核心优势

1. **硬件加速**：支持 AI 推理、图像处理、视频编解码等硬件加速功能
2. **丰富接口**：提供 GPIO、I2C、SPI、UART 等完整的硬件接口
3. **AI 能力**：内置 KPU 神经网络处理器，支持多种 AI 模型
4. **多媒体**：强大的图像、视频、音频处理能力
5. **易用性**：Python 编程，简单易学，快速开发

### 📚 学习路径建议

1. **入门阶段**：从 Pin、utime 等基础模块开始
2. **进阶阶段**：学习 Sensor、Display 等多媒体模块
3. **高级阶段**：掌握 nncase_runtime、AI2D 等 AI 模块
4. **项目实战**：结合多个模块开发完整应用

### 🔧 开发建议

- 使用官方 IDE 进行开发调试
- 参考官方例程快速上手
- 关注内存管理，及时调用 `gc.collect()`
- 合理使用硬件资源，避免冲突
- 定期查看官方文档更新

### 📞 技术支持

如遇到问题，可通过以下渠道获取帮助：

- **官方文档**：[https://www.kendryte.com/zh/document](https://www.kendryte.com/zh/document)
- **问答社区**：[https://www.kendryte.com/answer](https://www.kendryte.com/answer)
- **QQ 群**：578895334
- **GitHub**：[https://github.com/kendryte/k230_canmv_docs](https://github.com/kendryte/k230_canmv_docs)

---

*本文档基于 CanMV K230 官方 API 手册整理，详细信息请参考官方文档。文档版本：v1.3，更新时间：2025年1月*

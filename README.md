# 简易自行瞄准装置 - 基本要求第2问

## 项目概述

本项目实现了2023年电赛E题的基本要求第2问，包含以下功能：

1. **矩形识别**：自动识别黑色矩形目标
2. **激光笔控制**：GPIO33控制紫色激光笔
3. **二维步进电机控制**：实现X-Y轴精确定位
4. **脱机调阈值功能**：触摸屏调整识别阈值

## 硬件连接

### 激光笔模块
- **GPIO33**：激光笔控制（高电平开启）

### 步进电机引脚
- **X轴电机**：GPIO15, 16, 17, 19
- **Y轴电机**：GPIO27, 14, 61, 40

### 传感器设置
- 启用了镜像（hmirror）和翻转（vflip）
- 分辨率：800x480

## 文件说明

### 主要文件

1. **`main.py`** - 完整功能版本
   - 包含所有功能：矩形识别、激光控制、电机控制、脱机调阈值
   - 支持触摸屏交互

2. **`main_optimized.py`** - 优化版本 ⭐推荐⭐
   - 使用配置文件，代码结构更清晰
   - 面向对象设计，易于维护和扩展
   - 包含完整的错误处理和资源管理

3. **`basic_requirement_2.py`** - 简化版本
   - 专注于基本要求第2问的核心功能
   - 自动识别矩形并瞄准，激光发射4秒

4. **`test_rectangle_detection.py`** - 矩形识别测试
   - 用于测试矩形识别功能
   - 显示检测结果和二值化预览

5. **`hardware_test.py`** - 硬件测试脚本
   - 测试激光笔、步进电机、触摸屏、摄像头
   - 用于验证硬件连接是否正确

6. **`config.py`** - 配置文件
   - 集中管理所有参数设置
   - 方便调整和优化系统性能

## 功能特点

### 1. 矩形识别
- 使用灰度化 + 二值化 + 轮廓检测
- 自动选择最大矩形作为目标
- 计算矩形中心坐标
- 绘制矩形边框和中心点

### 2. 自动瞄准
- 计算目标与图像中心的偏差
- PID控制步进电机调整方向
- 死区设置避免抖动
- 瞄准成功后自动发射激光

### 3. 激光控制
- 瞄准成功后开启激光笔
- 发射持续4秒后自动关闭
- 实时显示激光状态

### 4. 脱机调阈值
- 长按屏幕进入调阈值模式
- 实时预览二值化效果
- 触摸按钮调整上下限
- 保存设置并返回正常模式

## 使用方法

### 🚀 快速开始

1. **硬件测试**（推荐第一步）
```bash
python hardware_test.py
```

2. **运行优化版本**（推荐）
```bash
python main_optimized.py
```

3. **运行完整版本**
```bash
python main.py
```

4. **运行简化版本**
```bash
python basic_requirement_2.py
```

5. **测试矩形识别**
```bash
python test_rectangle_detection.py
```

### 📋 使用步骤

1. **准备工作**
   - 确保硬件连接正确
   - 运行 `hardware_test.py` 验证硬件
   - 准备黑色矩形目标（建议A4纸上贴黑胶带）

2. **系统启动**
   - 运行主程序
   - 等待系统初始化完成
   - 观察摄像头画面

3. **阈值调整**（如需要）
   - 长按屏幕1.5秒进入调阈值模式
   - 观察右侧二值化预览
   - 调整阈值使目标矩形显示为白色
   - 点击"保存"确认设置

4. **自动瞄准**
   - 将矩形目标放在摄像头视野内
   - 系统自动识别并绘制绿色边框
   - 步进电机自动调整瞄准方向
   - 瞄准成功后激光自动发射4秒

## 操作说明

### 正常模式
1. 系统启动后自动进入矩形识别模式
2. 检测到矩形后显示绿色边框和红色中心点
3. 自动控制步进电机调整瞄准方向
4. 瞄准成功后激光笔自动开启4秒

### 阈值调整模式
1. 长按屏幕约1.5秒进入调阈值模式
2. 右侧显示二值化预览效果
3. 点击按钮调整阈值上下限：
   - "下限-/+" 调整二值化下限
   - "上限-/+" 调整二值化上限
4. 点击"保存"确认设置
5. 点击"返回"退出调阈值模式

## 技术参数

### 识别精度
- 死区设置：±15像素
- 矩形最小面积：8000像素
- 识别帧率：约30FPS

### 控制精度
- 步进电机步进角度：1.8°
- 定时器周期：5ms
- 激光发射时间：4秒

### 阈值范围
- 默认阈值：(80, 255)
- 调整步长：5
- 范围：0-255

## 注意事项

1. **安全警告**：激光笔使用时注意安全，避免直射眼睛
2. **硬件连接**：确保所有GPIO引脚连接正确
3. **环境光线**：识别效果受环境光线影响，可通过调阈值优化
4. **矩形要求**：目标矩形应为黑色，背景为白色或浅色
5. **距离适应**：支持远近距离和斜方向矩形的识别

## 故障排除

### 识别不到矩形
- 检查阈值设置是否合适
- 确认矩形颜色对比度
- 调整环境光线

### 电机不转动
- 检查GPIO引脚连接
- 确认电机驱动电路
- 检查电源供应

### 激光笔不亮
- 检查GPIO33连接
- 确认激光笔电源
- 检查控制逻辑

## 扩展功能

可以在现有基础上扩展：
- 多目标识别和优先级选择
- PID参数自适应调整
- 激光功率控制
- 运动轨迹记录
- 远程控制接口

## 技术支持

如有问题请检查：
1. 硬件连接是否正确
2. 代码是否完整运行
3. 环境设置是否合适
4. 参数配置是否正确

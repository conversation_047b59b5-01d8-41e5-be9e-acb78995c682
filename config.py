# 配置文件 - 简易自行瞄准装置参数设置

# GPIO引脚配置
GPIO_CONFIG = {
    # 激光笔控制引脚
    'LASER_PIN': 33,
    
    # X轴步进电机引脚
    'MOTOR_X_PINS': [15, 16, 17, 19],
    
    # Y轴步进电机引脚  
    'MOTOR_Y_PINS': [27, 14, 61, 40],
}

# 图像处理参数
IMAGE_CONFIG = {
    # 传感器分辨率
    'SENSOR_WIDTH': 800,
    'SENSOR_HEIGHT': 480,
    
    # 感兴趣区域 (x, y, w, h)
    'ROI': (0, 0, 800, 480),
    
    # 图像中心点
    'CENTER_X': 400,
    'CENTER_Y': 240,
    
    # 启用镜像和翻转
    'HMIRROR': True,
    'VFLIP': True,
}

# 矩形识别参数
DETECTION_CONFIG = {
    # 默认阈值 (下限, 上限)
    'DEFAULT_THRESHOLD': (80, 255),
    
    # 矩形最小面积
    'MIN_RECT_AREA': 8000,
    
    # 阈值调整步长
    'THRESHOLD_STEP': 5,
}

# 控制参数
CONTROL_CONFIG = {
    # 瞄准死区 (像素)
    'DEAD_ZONE': 15,
    
    # 步进电机定时器周期 (ms)
    'MOTOR_TIMER_PERIOD': 5,
    
    # 激光发射持续时间 (ms)
    'LASER_DURATION': 4000,
    
    # 触摸长按阈值 (帧数)
    'TOUCH_LONG_PRESS': 30,
}

# 步进电机控制序列
MOTOR_STEPS = [
    [1, 1, 0, 0],  # 步骤1
    [0, 1, 1, 0],  # 步骤2
    [0, 0, 1, 1],  # 步骤3
    [1, 0, 0, 1],  # 步骤4
]

# 显示配置
DISPLAY_CONFIG = {
    # 界面颜色
    'BUTTON_COLOR': (100, 100, 100),
    'TEXT_COLOR': (255, 255, 255),
    'SUCCESS_COLOR': (0, 255, 0),
    'ERROR_COLOR': (255, 0, 0),
    'WARNING_COLOR': (255, 255, 0),
    'INFO_COLOR': (255, 255, 255),
    
    # 字体大小
    'TITLE_FONT_SIZE': 30,
    'NORMAL_FONT_SIZE': 25,
    'SMALL_FONT_SIZE': 20,
    
    # 线条粗细
    'RECT_THICKNESS': 3,
    'LINE_THICKNESS': 2,
    'CIRCLE_RADIUS': 8,
}

# 调试配置
DEBUG_CONFIG = {
    # 是否显示调试信息
    'SHOW_DEBUG_INFO': True,
    
    # 是否显示FPS
    'SHOW_FPS': True,
    
    # 是否显示二值化预览
    'SHOW_BINARY_PREVIEW': True,
    
    # 是否打印日志
    'PRINT_LOG': True,
}

# 安全配置
SAFETY_CONFIG = {
    # 激光最大连续工作时间 (ms)
    'MAX_LASER_TIME': 10000,
    
    # 电机最大连续工作时间 (ms)
    'MAX_MOTOR_TIME': 30000,
    
    # 系统过热保护温度 (°C)
    'MAX_TEMPERATURE': 70,
}

def get_config():
    """获取完整配置"""
    return {
        'gpio': GPIO_CONFIG,
        'image': IMAGE_CONFIG,
        'detection': DETECTION_CONFIG,
        'control': CONTROL_CONFIG,
        'motor_steps': MOTOR_STEPS,
        'display': DISPLAY_CONFIG,
        'debug': DEBUG_CONFIG,
        'safety': SAFETY_CONFIG,
    }

def print_config():
    """打印当前配置"""
    config = get_config()
    print("=== 系统配置 ===")
    for category, settings in config.items():
        print(f"\n[{category.upper()}]")
        if isinstance(settings, dict):
            for key, value in settings.items():
                print(f"  {key}: {value}")
        else:
            print(f"  {settings}")

if __name__ == "__main__":
    print_config()
